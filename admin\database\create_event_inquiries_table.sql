-- Create event_inquiries table for storing event contact form submissions
CREATE TABLE IF NOT EXISTS `event_inquiries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `event_title` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `status` enum('new','contacted','resolved') DEFAULT 'new',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `event_id` (`event_id`),
  KEY `email` (`email`),
  KEY `status` (`status`),
  <PERSON><PERSON>Y `created_at` (`created_at`),
  CONSTRAINT `event_inquiries_ibfk_1` FOR<PERSON><PERSON>N KEY (`event_id`) REFERENCES `upcoming_events` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
