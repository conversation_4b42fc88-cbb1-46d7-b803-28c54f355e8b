<?php
// Set content type for JSON response
header('Content-Type: application/json');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Initialize response array
$response = ['success' => false, 'message' => ''];

try {
    // Check if request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Validate and sanitize input data
    $event_id = isset($_POST['event_id']) ? intval($_POST['event_id']) : 0;
    $event_title = isset($_POST['event_title']) ? trim($_POST['event_title']) : '';
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $company = isset($_POST['company']) ? trim($_POST['company']) : '';
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';
    $agree_terms = isset($_POST['agree_terms']) ? true : false;

    // Validation
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Name is required';
    }

    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }

    if (empty($message)) {
        $errors[] = 'Message is required';
    }

    if (!$agree_terms) {
        $errors[] = 'You must agree to receive communications';
    }

    if (!empty($errors)) {
        throw new Exception(implode(', ', $errors));
    }

    // Prepare email content
    $to = '<EMAIL>';
    $subject = 'Event Inquiry: ' . $event_title;

    // Create email body
    $email_body = "
    <html>
    <head>
        <title>Event Inquiry - {$event_title}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background-color: #2a1c19; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .event-info { background-color: #f8f9fa; padding: 15px; border-left: 4px solid #2a1c19; margin: 20px 0; }
            .contact-info { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .footer { background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px; }
            .label { font-weight: bold; color: #2a1c19; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>New Event Inquiry</h2>
            <p>Baraaro Fruit Empire</p>
        </div>

        <div class='content'>
            <div class='event-info'>
                <h3>Event Information</h3>
                <p><span class='label'>Event:</span> {$event_title}</p>
                <p><span class='label'>Event ID:</span> #{$event_id}</p>
            </div>

            <div class='contact-info'>
                <h3>Contact Information</h3>
                <p><span class='label'>Name:</span> " . htmlspecialchars($name) . "</p>
                <p><span class='label'>Email:</span> " . htmlspecialchars($email) . "</p>
                <p><span class='label'>Phone:</span> " . (!empty($phone) ? htmlspecialchars($phone) : 'Not provided') . "</p>
                <p><span class='label'>Company/Organization:</span> " . (!empty($company) ? htmlspecialchars($company) : 'Not provided') . "</p>
            </div>

            <div>
                <h3>Message</h3>
                <p>" . nl2br(htmlspecialchars($message)) . "</p>
            </div>

            <div style='margin-top: 30px; padding: 15px; background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px;'>
                <p><strong>Note:</strong> The customer has agreed to receive communications about events.</p>
                <p><strong>Submitted on:</strong> " . date('Y-m-d H:i:s') . "</p>
            </div>
        </div>

        <div class='footer'>
            <p>This inquiry was submitted through the Baraaro Fruit Empire website.</p>
        </div>
    </body>
    </html>
    ";

    // Email headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: <EMAIL>',
        'Reply-To: ' . $email,
        'X-Mailer: PHP/' . phpversion()
    ];

    // Send email
    $mail_sent = mail($to, $subject, $email_body, implode("\r\n", $headers));

    if ($mail_sent) {
        // Optional: Log the inquiry to database for tracking
        try {
            require_once 'admin/database/conn.php';

            // Check if event_inquiries table exists, create if not
            $table_check = $pdo->query("SHOW TABLES LIKE 'event_inquiries'");
            if ($table_check->rowCount() == 0) {
                // Create the table
                $create_table_sql = "
                CREATE TABLE IF NOT EXISTS `event_inquiries` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) NOT NULL,
                  `event_title` varchar(255) NOT NULL,
                  `name` varchar(255) NOT NULL,
                  `email` varchar(255) NOT NULL,
                  `phone` varchar(20) DEFAULT NULL,
                  `company` varchar(255) DEFAULT NULL,
                  `message` text NOT NULL,
                  `status` enum('new','contacted','resolved') DEFAULT 'new',
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `event_id` (`event_id`),
                  KEY `email` (`email`),
                  KEY `status` (`status`),
                  KEY `created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
                ";
                $pdo->exec($create_table_sql);
            }

            $stmt = $pdo->prepare("
                INSERT INTO event_inquiries
                (event_id, event_title, name, email, phone, company, message, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $event_id,
                $event_title,
                $name,
                $email,
                $phone,
                $company,
                $message
            ]);
        } catch (Exception $db_error) {
            // Log database error but don't fail the email sending
            error_log("Database logging error: " . $db_error->getMessage());
        }

        $response['success'] = true;
        $response['message'] = 'Your inquiry has been sent successfully! We will contact you soon.';
    } else {
        throw new Exception('Failed to send email. Please try again or contact us directly.');
    }

} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();

    // Log error for debugging
    error_log("Event inquiry error: " . $e->getMessage());
}

// Return JSON response
echo json_encode($response);
exit;
?>
