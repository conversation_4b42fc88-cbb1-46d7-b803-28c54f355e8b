<?php
// Debug script for events functionality
// This script helps diagnose issues with the events system

// Include database connection
require_once 'database/conn.php';

echo "<h1>Events System Debug</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; }</style>";

// Step 1: Check database connection
echo "<h3>1. Database Connection</h3>";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<span class='success'>✅ Database connection successful</span><br>";
    echo "Database: " . $pdo->query("SELECT DATABASE()")->fetchColumn() . "<br>";
} catch (Exception $e) {
    echo "<span class='error'>❌ Database connection failed: " . $e->getMessage() . "</span><br>";
    exit;
}

// Step 2: Check if upcoming_events table exists
echo "<h3>2. Upcoming Events Table Check</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'upcoming_events'");
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "<span class='success'>✅ upcoming_events table exists</span><br>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE upcoming_events");
        $columns = $stmt->fetchAll();
        echo "📋 Table structure:<br>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // Check if table has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM upcoming_events");
        $count = $stmt->fetch()['count'];
        echo "📊 Records in table: {$count}<br>";
        
        if ($count > 0) {
            echo "<h4>Sample Records:</h4>";
            $stmt = $pdo->query("SELECT id, title, category, event_date, created_at FROM upcoming_events LIMIT 5");
            $events = $stmt->fetchAll();
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Title</th><th>Category</th><th>Event Date</th><th>Created</th></tr>";
            foreach ($events as $event) {
                echo "<tr>";
                echo "<td>{$event['id']}</td>";
                echo "<td>{$event['title']}</td>";
                echo "<td>{$event['category']}</td>";
                echo "<td>{$event['event_date']}</td>";
                echo "<td>{$event['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<span class='warning'>⚠️ upcoming_events table does not exist</span><br>";
        echo "<p>The table will be created automatically when you visit events.php</p>";
        
        // Try to create the table
        echo "<h4>Creating Table...</h4>";
        try {
            $create_table_sql = "
            CREATE TABLE IF NOT EXISTS `upcoming_events` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `category` varchar(200) NOT NULL,
              `title` varchar(255) NOT NULL,
              `location` varchar(255) NOT NULL,
              `price` decimal(25,2) NOT NULL DEFAULT 0.00,
              `seats` int(11) NOT NULL,
              `image` varchar(255) NOT NULL,
              `event_date` date NOT NULL,
              `event_starting_time` time NOT NULL,
              `event_ending_time` time NOT NULL,
              `host` varchar(255) NOT NULL,
              `user_id` int(11) NOT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `event_date` (`event_date`),
              KEY `category` (`category`),
              CONSTRAINT `upcoming_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
            
            $pdo->exec($create_table_sql);
            echo "<span class='success'>✅ Table created successfully!</span><br>";
        } catch (Exception $e) {
            echo "<span class='error'>❌ Failed to create table: " . $e->getMessage() . "</span><br>";
        }
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Error checking table: " . $e->getMessage() . "</span><br>";
}

// Step 3: Check uploads directory
echo "<h3>3. Upload Directory Check</h3>";
$upload_dir = 'uploads/events/';
if (is_dir($upload_dir)) {
    echo "<span class='success'>✅ Upload directory exists: {$upload_dir}</span><br>";
    if (is_writable($upload_dir)) {
        echo "<span class='success'>✅ Upload directory is writable</span><br>";
    } else {
        echo "<span class='error'>❌ Upload directory is not writable</span><br>";
    }
} else {
    echo "<span class='warning'>⚠️ Upload directory does not exist: {$upload_dir}</span><br>";
    echo "Attempting to create directory...<br>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "<span class='success'>✅ Upload directory created successfully</span><br>";
    } else {
        echo "<span class='error'>❌ Failed to create upload directory</span><br>";
    }
}

// Step 4: Check users table (for foreign key constraint)
echo "<h3>4. Users Table Check</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $users_table_exists = $stmt->rowCount() > 0;
    
    if ($users_table_exists) {
        echo "<span class='success'>✅ users table exists</span><br>";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $user_count = $stmt->fetch()['count'];
        echo "👥 Users in system: {$user_count}<br>";
    } else {
        echo "<span class='error'>❌ users table does not exist - this will cause foreign key constraint errors</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Error checking users table: " . $e->getMessage() . "</span><br>";
}

// Step 5: Test basic operations
echo "<h3>5. System Status</h3>";
if ($table_exists && $users_table_exists) {
    echo "<span class='success'>✅ Events system should be working properly</span><br>";
    echo "<p><a href='events.php'>Go to Events Management</a></p>";
} else {
    echo "<span class='warning'>⚠️ Some issues detected. Please fix the above errors before using the events system.</span><br>";
}

echo "<hr>";
echo "<p><small>Debug completed at " . date('Y-m-d H:i:s') . "</small></p>";
?>
