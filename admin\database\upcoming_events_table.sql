-- Upcoming Events table structure for event management
-- This table stores event information with image file paths

CREATE TABLE IF NOT EXISTS `upcoming_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(200) NOT NULL,
  `title` varchar(255) NOT NULL,
  `location` varchar(255) NOT NULL,
  `price` decimal(25,2) NOT NULL DEFAULT 0.00,
  `seats` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `event_date` date NOT NULL,
  `event_starting_time` time NOT NULL,
  `event_ending_time` time NOT NULL,
  `host` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `event_date` (`event_date`),
  KEY `category` (`category`),
  CONSTRAINT `upcoming_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Sample data (optional)
-- INSERT INTO `upcoming_events` (`category`, `title`, `location`, `price`, `seats`, `image`, `event_date`, `event_starting_time`, `event_ending_time`, `host`, `user_id`) VALUES
-- ('Conference', 'Tech Innovation Summit 2025', 'Convention Center, Downtown', 150.00, 500, 'uploads/events/sample1.jpg', '2025-06-15', '09:00:00', '17:00:00', 'Tech Innovators Inc.', 1),
-- ('Workshop', 'Digital Marketing Masterclass', 'Business Hub, Main Street', 75.00, 50, 'uploads/events/sample2.jpg', '2025-06-20', '10:00:00', '16:00:00', 'Marketing Experts', 1),
-- ('Webinar', 'Future of AI in Business', 'Online Event', 0.00, 1000, 'uploads/events/sample3.jpg', '2025-06-25', '14:00:00', '15:30:00', 'AI Research Group', 1);
